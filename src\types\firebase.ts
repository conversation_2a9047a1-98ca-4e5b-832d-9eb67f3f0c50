// Firebase/Firestore TypeScript types
import { Timestamp } from 'firebase/firestore';

// User role enum
export type UserRole = 'admin' | 'teacher' | 'parent';

// Base interface for all documents
export interface BaseDocument {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// User profile (replaces Supabase profiles table)
export interface User extends BaseDocument {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  address?: string;
  phoneNumber?: string;
  avatarUrl?: string;
}

// Student record
export interface Student extends BaseDocument {
  studentId: string; // e.g., S2401234
  firstName: string;
  lastName: string;
  dateOfBirth: Timestamp;
  gradeLevel: string;
  gender?: string;
  address?: string;
  parentId: string; // Reference to User document
  photoUrl?: string;
  isActive: boolean;
  enrollmentDate: Timestamp;
}

// Classroom
export interface Classroom extends BaseDocument {
  name: string;
  gradeLevel: string;
  teacherId?: string; // Reference to User document
  capacity: number;
  description?: string;
  roomNumber?: string;
  academicYear: string;
  isActive: boolean;
}

// Financial record
export interface FinancialRecord extends BaseDocument {
  recordType: 'income' | 'expense';
  category: string;
  amount: number;
  recordDate: Timestamp;
  description: string;
  paymentMethod?: string;
  referenceNumber?: string;
  studentId?: string; // Reference to Student document
  recordedBy?: string; // Reference to User document
}

// Inventory item
export interface InventoryItem extends BaseDocument {
  name: string;
  category: string;
  quantity: number;
  unitPrice?: number;
  minimumStockLevel?: number;
  location?: string;
  lastRestocked?: Timestamp;
  supplier?: string;
}

// Health record
export interface HealthRecord extends BaseDocument {
  studentId: string; // Reference to Student document
  recordType: string;
  recordDate: Timestamp;
  details: any; // JSON object for flexible health data
  isCritical: boolean;
  recordedBy?: string; // Reference to User document
}

// Subject
export interface Subject extends BaseDocument {
  name: string;
  gradeLevel: string;
  description?: string;
}

// Attendance record
export interface AttendanceRecord extends BaseDocument {
  studentId: string; // Reference to Student document
  date: Timestamp;
  status: 'present' | 'absent' | 'late';
  notes?: string;
  recordedBy: string; // Reference to User document
}

// Alert/Notification
export interface Alert extends BaseDocument {
  title: string;
  message: string;
  alertType: string;
  severity: string;
  forRole?: UserRole;
  isActive: boolean;
  expiresAt?: Timestamp;
  createdBy?: string; // Reference to User document
}

// Timetable slot
export interface TimetableSlot extends BaseDocument {
  day: string;
  time: string;
  classroomId: string; // Reference to Classroom document
  subjectId: string; // Reference to Subject document
  teacherId: string; // Reference to User document
}

// Query condition interface for Firestore queries
export interface QueryCondition {
  field: string;
  operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'array-contains' | 'array-contains-any' | 'in' | 'not-in';
  value: any;
}

// API response interface
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}
