# Firebase Migration Guide

This document outlines the migration from Supabase to Firebase for the Promise Academy Bloom application.

## Prerequisites

1. **Firebase Project Setup**
   - Create a new Firebase project at https://console.firebase.google.com
   - Enable Authentication (Email/Password provider)
   - Enable Firestore Database
   - Get your Firebase configuration values

2. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Replace placeholder values with your Firebase configuration
   - Never commit the `.env` file to version control

## Installation

Run the following commands to install Firebase and remove Supabase:

```bash
# Install Firebase SDK
npm install firebase

# Remove Supabase dependency
npm uninstall @supabase/supabase-js
```

## Migration Progress

### ✅ Phase 1: Foundation Setup (COMPLETED)
- [x] Firebase SDK configuration files created
- [x] Authentication utilities implemented
- [x] Firestore database utilities created
- [x] TypeScript types defined
- [x] Environment variables template created

### 🔄 Phase 2: Authentication Migration (PENDING)
- [ ] Update LoginForm component
- [ ] Update UserRegistrationForm component
- [ ] Implement auth state management

### 🔄 Phase 3: Core Components Migration (PENDING)
- [ ] Update Header component
- [ ] Update DashboardLayout component
- [ ] Implement route protection

### 🔄 Phase 4: Data Management Migration (PENDING)
- [ ] Migrate user management utilities
- [ ] Update StudentsManagement component

### 🔄 Phase 5: Admin Components Migration (PENDING)
- [ ] Update FinancialManagement component
- [ ] Update InventoryManagement component
- [ ] Update ClassroomManagement component
- [ ] Update TimetableManagement component

### 🔄 Phase 6: Dashboard Pages Migration (PENDING)
- [ ] Update AdminDashboard page
- [ ] Update TeacherDashboard page
- [ ] Update ParentDashboard page

### 🔄 Phase 7: Cleanup and Optimization (PENDING)
- [ ] Remove Supabase integration files
- [ ] Clean up unused imports
- [ ] Performance optimization

## Key Files Created

- `src/lib/firebase.ts` - Firebase initialization and configuration
- `src/lib/auth.ts` - Authentication utilities and user management
- `src/lib/firestore.ts` - Database operations and CRUD utilities
- `src/types/firebase.ts` - TypeScript type definitions
- `.env.example` - Environment variables template

## Database Schema Mapping

| Supabase Table | Firestore Collection | Notes |
|----------------|---------------------|-------|
| profiles | users | User profile data |
| students | students | Student records |
| classrooms | classrooms | Classroom information |
| financial_records | financial | Financial transactions |
| inventory_items | inventory | Inventory management |
| health_records | health | Student health records |
| subjects | subjects | Subject definitions |
| attendance_records | attendance | Attendance tracking |
| alerts | alerts | System notifications |
| timetable_slots | timetables | Schedule management |

## Authentication Changes

| Supabase Method | Firebase Equivalent |
|----------------|-------------------|
| `supabase.auth.signInWithPassword()` | `signInWithEmailAndPassword()` |
| `supabase.auth.signUp()` | `createUserWithEmailAndPassword()` |
| `supabase.auth.signOut()` | `signOut()` |
| `supabase.auth.getSession()` | `onAuthStateChanged()` |

## Next Steps

1. Set up your Firebase project and get configuration values
2. Create the `.env` file with your Firebase configuration
3. Run the package installation commands
4. Continue with Phase 2: Authentication Migration

## Support

If you encounter any issues during the migration, refer to:
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)
- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)
