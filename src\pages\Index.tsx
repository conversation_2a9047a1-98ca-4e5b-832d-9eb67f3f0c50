
import { useRef } from 'react';
import Hero from '@/components/Hero';
import Features from '@/components/Features';
import Testimonials from '@/components/Testimonials';
import About from '@/components/About';
import Programs from '@/components/Programs';
import Contact from '@/components/Contact';
import Footer from '@/components/Footer';
import Header from '@/components/Header';

const Index = () => {
  // Create refs for scroll functionality
  const aboutRef = useRef<HTMLDivElement>(null);
  const programsRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);
  
  // Scroll functions
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header 
        onAboutClick={() => scrollToSection(aboutRef)}
        onProgramsClick={() => scrollToSection(programsRef)}
        onContactClick={() => scrollToSection(contactRef)}
      />
      <main className="flex-1">
        <Hero />
        <div ref={aboutRef}>
          <About />
        </div>
        <Features />
        <div ref={programsRef}>
          <Programs />
        </div>
        <Testimonials />
        <div ref={contactRef}>
          <Contact />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Index;
