// Create demo users for Firebase
import { signUp } from '@/lib/auth';

export async function createDemoUsers() {
  const demoUsers = [
    {
      email: '<EMAIL>',
      password: 'admin123',
      userData: {
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin' as const,
        address: '123 School Admin St, Education City, EC 12345',
        phoneNumber: '+****************'
      }
    },
    {
      email: '<EMAIL>',
      password: 'teacher123',
      userData: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'teacher' as const,
        address: '456 Teacher Ave, Education City, EC 12346',
        phoneNumber: '+****************'
      }
    },
    {
      email: '<EMAIL>',
      password: 'parent123',
      userData: {
        firstName: 'John',
        lastName: 'Smith',
        role: 'parent' as const,
        address: '789 Parent Blvd, Education City, EC 12347',
        phoneNumber: '+****************'
      }
    }
  ];

  console.log('Creating demo users...');

  for (const demoUser of demoUsers) {
    try {
      const { user, profile, error } = await signUp(
        demoUser.email,
        demoUser.password,
        demoUser.userData
      );

      if (error) {
        // User might already exist, which is fine for demo purposes
        if (error.includes('email-already-in-use')) {
          console.log(`Demo user ${demoUser.email} already exists, skipping...`);
        } else {
          console.error(`Error creating demo user ${demoUser.email}:`, error);
        }
      } else if (user && profile) {
        console.log(`Demo user created: ${demoUser.email} (${profile.role})`);
      }
    } catch (error) {
      console.error(`Failed to create demo user ${demoUser.email}:`, error);
    }
  }

  console.log('Demo user creation completed.');
}
