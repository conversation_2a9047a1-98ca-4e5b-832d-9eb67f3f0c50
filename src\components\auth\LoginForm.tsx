
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { signIn, getUserProfile } from "@/lib/auth";
import type { UserRole } from "@/lib/auth";
import { Loader2 } from "lucide-react";



const LoginForm = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState<UserRole>("parent");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Sign in with Firebase
      const { user, error } = await signIn(email, password);

      if (error) {
        throw new Error(error);
      }

      if (user) {
        // Fetch user profile to get role and other details
        const profile = await getUserProfile(user.uid);

        if (!profile) {
          throw new Error("User profile not found");
        }

        // Store user info in local storage for quick access
        localStorage.setItem("user", JSON.stringify({
          email: user.email,
          role: profile.role,
          name: `${profile.firstName} ${profile.lastName}`,
          id: user.uid
        }));

        toast({
          title: "Login successful",
          description: `Welcome back, ${profile.firstName}!`,
        });

        navigate(`/dashboard/${profile.role}`);
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Failed to login. Please check your credentials.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-card">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-quicksand text-center">Login to Promise Academy</CardTitle>
        <CardDescription className="text-center">
          Enter your credentials to access your account
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="role">I am a</Label>
            <Select
              value={role}
              onValueChange={(value) => setRole(value as UserRole)}
            >
              <SelectTrigger id="role" className="w-full">
                <SelectValue placeholder="Select your role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="parent">Parent</SelectItem>
                <SelectItem value="teacher">Teacher</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email" 
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="password">Password</Label>
              <a 
                href="#" 
                className="text-sm text-promise-600 hover:text-promise-800 font-medium"
              >
                Forgot password?
              </a>
            </div>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-promise-500 hover:bg-promise-600"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              "Sign in"
            )}
          </Button>
        </form>
        
        <div className="text-center text-sm">
          <p className="text-gray-500">
            Demo Credentials:
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Parent: <EMAIL> / parent123
          </p>
          <p className="text-xs text-muted-foreground">
            Teacher: <EMAIL> / teacher123
          </p>
          <p className="text-xs text-muted-foreground">
            Admin: <EMAIL> / admin123
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <p className="text-xs text-gray-500 text-center">
          By logging in, you agree to our{" "}
          <a href="#" className="underline hover:text-promise-600">Terms of Service</a>{" "}
          and{" "}
          <a href="#" className="underline hover:text-promise-600">Privacy Policy</a>.
        </p>
      </CardFooter>
    </Card>
  );
};

export default LoginForm;
